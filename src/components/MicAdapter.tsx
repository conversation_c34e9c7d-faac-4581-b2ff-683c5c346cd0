import React from "react";
import { Mic } from "microapps";

type ConversationState =
  | "idle"
  | "listening"
  | "processing"
  | "speaking"
  | "error"
  | "reset";

interface MicAdapterProps {
  conversationState: ConversationState;
  isVoiceActive: boolean;     // tu flag actual
  micLevel: number;           // 0..1
  onToggle: () => void;       // tu toggleVoice
  voiceError?: string | null; // tu error si existe
  className?: string;
  id?: string;
}

function mapToMicState(
  st: ConversationState,
  isVoiceActive: boolean,
  hasError: boolean
): "default" | "recording" | "disabled" | "reset" {
  if (hasError) return "disabled";
  switch (st) {
    case "reset":
      return "reset";
    case "listening":
      // grabando / escuchando → animación del anillo de Mic
      return "recording";
    case "processing":
    case "speaking":
      // durante thinking/tts bloqueamos el botón
      return "disabled";
    case "idle":
    default:
      return isVoiceActive ? "recording" : "default";
  }
}

function shouldAllowClick(
  hasError: boolean,
  errorMessage?: string | null
): boolean {
  // Permitir clic si no hay error
  if (!hasError) return true;

  // Permitir clic para errores recuperables que permiten reintentar
  if (errorMessage) {
    // Errores que permiten reintento
    const recoverableErrors = [
      "No se detectó habla",
      "Error de red",
      "No se pudo acceder al micrófono",
      "No se pudo iniciar el reconocimiento de voz"
    ];

    return recoverableErrors.some(error => errorMessage.includes(error));
  }

  return false;
}

export const MicAdapter: React.FC<MicAdapterProps> = ({
  conversationState,
  isVoiceActive,
  micLevel,
  onToggle,
  voiceError,
  className,
  id,
}) => {
  const state = mapToMicState(
    conversationState,
    isVoiceActive,
    !!voiceError
  );

  const level = Math.max(0, Math.min(100, Math.round((micLevel ?? 0) * 100)));
  const allowClick = shouldAllowClick(!!voiceError, voiceError);

  // Función wrapper que maneja el clic según si está permitido
  const handleClick = () => {
    if (allowClick) {
      onToggle();
    }
  };

  return (
    <div className={className}>
      <div
        style={{
          position: 'relative',
          cursor: allowClick ? 'pointer' : 'not-allowed'
        }}
        onClick={handleClick}
      >
        <Mic
          id={id}
          className="game-mic"
          level={level}
          onClick={() => {}} // Deshabilitamos el onClick del Mic original
          state={state}
        />
        {/* Overlay invisible para capturar clics cuando está disabled */}
        {state === 'disabled' && allowClick && (
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              cursor: 'pointer',
              zIndex: 10
            }}
          />
        )}
      </div>
      {!!voiceError && (
        <div className="voice-error" style={{ color: "red", fontSize: 12, marginTop: 4 }}>
          {voiceError}
        </div>
      )}
    </div>
  );
};
